#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试触摸屏直接编辑模式下的checkbox功能
"""

import requests
import json
import time

def test_touchscreen_checkbox():
    """测试触摸屏直接编辑模式下的checkbox功能"""
    print("开始测试触摸屏直接编辑模式下的checkbox功能...")
    
    base_url = "http://localhost:8888"
    
    # 1. 首先调用直接编辑模板API
    print("\n=== 步骤1: 调用直接编辑模板API ===")
    direct_edit_data = {
        "template_path": "complaint_template.docx",
        "template_name": "测试模板"
    }
    
    response = requests.post(f"{base_url}/direct-edit-template", 
                           json=direct_edit_data,
                           headers={'Content-Type': 'application/json'})
    
    if response.status_code != 200:
        print(f"❌ 直接编辑API调用失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return False
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 直接编辑API返回失败: {result}")
        return False
    
    preview_id = result['preview_id']
    edit_url = result['edit_url']
    print(f"✅ 直接编辑API调用成功")
    print(f"Preview ID: {preview_id}")
    print(f"编辑URL: {edit_url}")
    
    # 2. 模拟用户在编辑页面勾选checkbox并提交
    print("\n=== 步骤2: 模拟用户勾选checkbox并生成文档 ===")
    
    # 模拟用户填写的表单数据（包含勾选的checkbox）
    form_data = {
        "原告姓名": "张三",
        "被告姓名": "李四",
        "checkbox_1": True,   # 第1个checkbox勾选
        "checkbox_2": False,  # 第2个checkbox不勾选
        "checkbox_3": True,   # 第3个checkbox勾选
        "checkbox_4": False,  # 第4个checkbox不勾选
        "checkbox_5": True,   # 第5个checkbox勾选
        "checkbox_6": False,  # 第6个checkbox不勾选
    }
    
    generate_data = {
        "preview_id": preview_id,
        "form_data": form_data
    }
    
    print(f"提交的表单数据: {json.dumps(form_data, ensure_ascii=False, indent=2)}")
    
    response = requests.post(f"{base_url}/api/generate-from-preview",
                           json=generate_data,
                           headers={'Content-Type': 'application/json'})
    
    if response.status_code != 200:
        print(f"❌ 文档生成API调用失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return False
    
    result = response.json()
    if not result.get('success'):
        print(f"❌ 文档生成API返回失败: {result}")
        return False
    
    download_url = result['download_url']
    filename = result['filename']
    print(f"✅ 文档生成成功")
    print(f"下载URL: {download_url}")
    print(f"文件名: {filename}")
    
    # 3. 验证生成的文档（这里只是打印信息，实际验证需要下载并检查文档内容）
    print("\n=== 步骤3: 验证结果 ===")
    print("✅ 触摸屏直接编辑模式下的checkbox功能测试完成")
    print("📝 请手动检查生成的文档，确认checkbox状态是否正确：")
    print("   - checkbox_1 应该被勾选 ☑")
    print("   - checkbox_2 应该未勾选 ☐")
    print("   - checkbox_3 应该被勾选 ☑")
    print("   - checkbox_4 应该未勾选 ☐")
    print("   - checkbox_5 应该被勾选 ☑")
    print("   - checkbox_6 应该未勾选 ☐")
    
    return True

if __name__ == "__main__":
    try:
        success = test_touchscreen_checkbox()
        if success:
            print("\n🎉 测试完成！")
        else:
            print("\n❌ 测试失败！")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
